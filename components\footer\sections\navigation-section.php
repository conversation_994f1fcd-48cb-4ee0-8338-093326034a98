<?php
/**
 * Footer Navigation Section
 */

// Get footer sections visibility settings
$show_company = getThemeOption('footer_show_company', '1') === '1';
$show_services = getThemeOption('footer_show_services', '1') === '1';
$show_projects = getThemeOption('footer_show_projects', '1') === '1';

// Get section titles
$company_title = getThemeOption('footer_company_title', 'Company');
$services_title = getThemeOption('footer_services_title', 'Services');
$projects_title = getThemeOption('footer_projects_title', 'Projects');

// Get custom links or use defaults
$company_links = json_decode(getThemeOption('footer_company_links', ''), true) ?: [
    ['title' => 'Home', 'url' => ''],
    ['title' => 'About Us', 'url' => 'about'],
    ['title' => 'Our Services', 'url' => 'services'],
    ['title' => 'Our Team', 'url' => 'team'],
    ['title' => 'Projects', 'url' => 'projects'],
    ['title' => 'Blog', 'url' => 'blog'],
    ['title' => 'News & Insights', 'url' => 'news'],
    ['title' => 'Careers', 'url' => 'careers'],
    ['title' => 'Contact', 'url' => 'contact'],
];

$services_links = json_decode(getThemeOption('footer_services_links', ''), true) ?: [
    ['title' => 'Architectural Design', 'url' => 'service/architectural-design'],
    ['title' => 'Structural Engineering', 'url' => 'service/structural-engineering'],
    ['title' => 'Construction Management', 'url' => 'service/construction-management'],
    ['title' => 'Sustainable Design', 'url' => 'service/sustainable-design'],
];

$projects_links = json_decode(getThemeOption('footer_projects_links', ''), true) ?: [
    ['title' => 'All Projects', 'url' => 'projects'],
    ['title' => 'Featured Work', 'url' => 'work'],
    ['title' => 'Commercial', 'url' => 'projects'],
    ['title' => 'Residential', 'url' => 'projects'],
];
?>

<?php if ($show_company): ?>
<div class="footer-nav-column">
    <h4 class="footer-nav-title"><?php echo htmlspecialchars($company_title); ?></h4>
    <ul class="footer-nav-list">
        <?php foreach ($company_links as $link): ?>
            <li><a href="<?php echo siteUrl($link['url']); ?>" class="footer-nav-link"><?php echo htmlspecialchars($link['title']); ?></a></li>
        <?php endforeach; ?>
    </ul>
</div>
<?php endif; ?>

<?php if ($show_services): ?>
<div class="footer-nav-column">
    <h4 class="footer-nav-title"><?php echo htmlspecialchars($services_title); ?></h4>
    <ul class="footer-nav-list">
        <?php foreach ($services_links as $link): ?>
            <li><a href="<?php echo siteUrl($link['url']); ?>" class="footer-nav-link"><?php echo htmlspecialchars($link['title']); ?></a></li>
        <?php endforeach; ?>
    </ul>
</div>
<?php endif; ?>

<?php if ($show_projects): ?>
<div class="footer-nav-column">
    <h4 class="footer-nav-title"><?php echo htmlspecialchars($projects_title); ?></h4>
    <ul class="footer-nav-list">
        <?php foreach ($projects_links as $link): ?>
            <li><a href="<?php echo siteUrl($link['url']); ?>" class="footer-nav-link"><?php echo htmlspecialchars($link['title']); ?></a></li>
        <?php endforeach; ?>
    </ul>
</div>
<?php endif; ?>
