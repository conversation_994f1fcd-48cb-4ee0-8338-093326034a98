<?php
/**
 * Project Details Page - Individual Project Showcase
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get project slug from URL
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ' . siteUrl('projects'));
    exit;
}

// Fetch project from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM projects WHERE slug = ? AND status = 'published'");
$stmt->execute([$slug]);
$project = $stmt->fetch();

if (!$project) {
    header('Location: ' . siteUrl('404'));
    exit;
}

// Get related projects
$stmt = $db->prepare("SELECT * FROM projects WHERE category = ? AND id != ? AND status = 'published' ORDER BY created_at DESC LIMIT 3");
$stmt->execute([$project['category'], $project['id']]);
$related_projects = $stmt->fetchAll();

$pageTitle = $project['title'] . ' - ' . SITE_NAME;
$pageDescription = $project['excerpt'] ?: 'Discover the details of our ' . $project['title'] . ' project.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Project-specific styles -->
    <style>
        .project-hero {
            height: 60vh;
            background-size: cover;
            background-position: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .project-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(26, 26, 26, 0.6);
            z-index: 1;
        }
        
        .project-hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 2rem;
        }
        
        .project-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .project-meta {
            background: #F5F5F5;
            padding: 3rem 0;
        }
        
        .project-meta-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .meta-item {
            text-align: center;
        }
        
        .meta-label {
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            font-size: 0.9rem;
        }
        
        .meta-value {
            font-size: 1.1rem;
            color: var(--text-color);
        }
        
        .project-content {
            padding: 4rem 0;
        }
        
        .project-gallery {
            padding: 4rem 0;
            background: #F5F5F5;
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .gallery-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            aspect-ratio: 4/3;
        }
        
        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover img {
            transform: scale(1.05);
        }
        
        .related-projects {
            padding: 4rem 0;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .project-hero h1 {
                font-size: 2rem;
            }
            
            .project-meta-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <!-- Project Hero -->
        <section class="project-hero" style="background-image: url('<?php echo $project['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop'; ?>');">
            <div class="project-hero-content">
                <h1><?php echo htmlspecialchars($project['title']); ?></h1>
                <p class="lead"><?php echo htmlspecialchars($project['excerpt']); ?></p>
            </div>
        </section>

        <!-- Project Meta -->
        <section class="project-meta">
            <div class="container">
                <div class="project-meta-grid">
                    <div class="meta-item">
                        <div class="meta-label">Client</div>
                        <div class="meta-value"><?php echo htmlspecialchars($project['client'] ?: 'Confidential'); ?></div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Location</div>
                        <div class="meta-value"><?php echo htmlspecialchars($project['location'] ?: 'Various'); ?></div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Category</div>
                        <div class="meta-value"><?php echo htmlspecialchars($project['category']); ?></div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Completion</div>
                        <div class="meta-value"><?php echo $project['completion_date'] ? date('Y', strtotime($project['completion_date'])) : 'Ongoing'; ?></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Project Content -->
        <section class="project-content">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="content">
                            <?php echo $project['content']; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Project Gallery -->
        <?php if (!empty($project['gallery'])): ?>
        <section class="project-gallery">
            <div class="container">
                <div class="section-header text-center">
                    <h2>Project Gallery</h2>
                    <p>Visual highlights from this project</p>
                </div>
                
                <div class="gallery-grid">
                    <?php 
                    $gallery_images = explode(',', $project['gallery']);
                    foreach ($gallery_images as $image): 
                        $image = trim($image);
                        if (!empty($image)):
                    ?>
                    <div class="gallery-item">
                        <img src="<?php echo strpos($image, 'http') === 0 ? $image : themeUrl('images/projects/' . $image); ?>" 
                             alt="<?php echo htmlspecialchars($project['title']); ?> Gallery" 
                             class="loading">
                    </div>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Related Projects -->
        <?php if (!empty($related_projects)): ?>
        <section class="related-projects">
            <div class="container">
                <div class="section-header text-center">
                    <h2>Related Projects</h2>
                    <p>More projects in the <?php echo htmlspecialchars($project['category']); ?> category</p>
                </div>
                
                <div class="related-grid">
                    <?php foreach ($related_projects as $related): ?>
                    <article class="related-card">
                        <img src="<?php echo $related['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=200&fit=crop'; ?>" 
                             alt="<?php echo htmlspecialchars($related['title']); ?>" 
                             class="loading">
                        <div class="related-card-content">
                            <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <h3><?php echo htmlspecialchars($related['title']); ?></h3>
                            <p><?php echo htmlspecialchars(substr($related['excerpt'], 0, 100)) . '...'; ?></p>
                            <a href="<?php echo siteUrl('project/' . $related['slug']); ?>" class="btn btn-outline">View Project</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- CTA Section -->
        <section class="cta-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2>Ready to Start Your Project?</h2>
                        <p class="lead">Let's discuss how we can bring your vision to life with our expertise in architectural design and engineering.</p>
                        <a href="<?php echo siteUrl('contact'); ?>" class="btn btn-primary">Get In Touch</a>
                        <a href="<?php echo siteUrl('projects'); ?>" class="btn btn-outline">View All Projects</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- Scripts -->
    <script src="<?php echo themeUrl('js/main.js'); ?>"></script>
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
