<?php
/**
 * News Details Page - Individual Article/Blog Post
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get news slug from URL
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ' . siteUrl('news'));
    exit;
}

// Fetch news article from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND status = 'published'");
$stmt->execute([$slug]);
$article = $stmt->fetch();

if (!$article) {
    header('Location: ' . siteUrl('404'));
    exit;
}

// Get related articles
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE category = ? AND id != ? AND status = 'published' ORDER BY published_at DESC LIMIT 3");
$stmt->execute([$article['category'], $article['id']]);
$related_articles = $stmt->fetchAll();

// Get recent articles if no related articles found
if (empty($related_articles)) {
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE id != ? AND status = 'published' ORDER BY published_at DESC LIMIT 3");
    $stmt->execute([$article['id']]);
    $related_articles = $stmt->fetchAll();
}

$pageTitle = $article['title'] . ' - ' . SITE_NAME;
$pageDescription = $article['excerpt'] ?: 'Read our latest insights on ' . $article['title'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Article-specific styles -->
    <style>
        .article-hero {
            height: 50vh;
            background-size: cover;
            background-position: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            min-height: 400px;
        }
        
        .article-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(rgba(26, 26, 26, 0.4), rgba(26, 26, 26, 0.7));
            z-index: 1;
        }
        
        .article-hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 2rem;
        }
        
        .article-hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            line-height: 1.2;
        }
        
        .article-meta {
            background: #F5F5F5;
            padding: 2rem 0;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .meta-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .meta-item strong {
            color: var(--accent-color);
        }
        
        .article-content {
            padding: 4rem 0;
        }
        
        .article-body {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
        }
        
        .article-body h2,
        .article-body h3,
        .article-body h4 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .article-body h2 {
            font-size: 1.8rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }
        
        .article-body h3 {
            font-size: 1.4rem;
            color: var(--accent-color);
        }
        
        .article-body p {
            margin-bottom: 1.5rem;
        }
        
        .article-body ul,
        .article-body ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }
        
        .article-body li {
            margin-bottom: 0.5rem;
        }
        
        .article-body blockquote {
            background: #F8F9FA;
            border-left: 4px solid var(--accent-color);
            padding: 1.5rem;
            margin: 2rem 0;
            font-style: italic;
        }
        
        .article-tags {
            background: #F8F9FA;
            padding: 2rem 0;
            border-top: 1px solid #E9ECEF;
        }
        
        .tags-list {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .tag {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .tag:hover {
            background: #d35400;
            color: white;
        }
        
        .related-articles {
            padding: 4rem 0;
            background: #F5F5F5;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-size: 1.2rem;
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .related-card .date {
            color: #666;
            font-size: 0.85rem;
            margin-top: 1rem;
        }
        
        .share-buttons {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid #E9ECEF;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .share-buttons h4 {
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        
        .share-link:hover {
            transform: scale(1.1);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-email { background: #666; }
        
        @media (max-width: 768px) {
            .article-hero h1 {
                font-size: 1.8rem;
            }
            
            .meta-info {
                gap: 1rem;
                justify-content: flex-start;
            }
            
            .article-body {
                font-size: 1rem;
            }
            
            .share-links {
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <!-- Article Hero -->
        <section class="article-hero" style="background-image: url('<?php echo $article['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop'; ?>');">
            <div class="article-hero-content">
                <h1><?php echo htmlspecialchars($article['title']); ?></h1>
                <p class="lead"><?php echo htmlspecialchars($article['excerpt']); ?></p>
            </div>
        </section>

        <!-- Article Meta -->
        <section class="article-meta">
            <div class="container">
                <div class="meta-info">
                    <div class="meta-item">
                        <strong>Author:</strong> <?php echo htmlspecialchars($article['author'] ?: 'Monolith Design Team'); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Published:</strong> <?php echo date('F j, Y', strtotime($article['published_at'])); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Category:</strong> <?php echo htmlspecialchars($article['category']); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Reading Time:</strong> <?php echo ceil(str_word_count(strip_tags($article['content'])) / 200); ?> min
                    </div>
                </div>
            </div>
        </section>

        <!-- Article Content -->
        <section class="article-content">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="article-body">
                            <?php echo $article['content']; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Share Buttons -->
        <section class="share-buttons">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <h4>Share This Article</h4>
                        <div class="share-links">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-facebook" target="_blank" rel="noopener">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>&text=<?php echo urlencode($article['title']); ?>" 
                               class="share-link share-twitter" target="_blank" rel="noopener">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-linkedin" target="_blank" rel="noopener">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="mailto:?subject=<?php echo urlencode($article['title']); ?>&body=<?php echo urlencode('Check out this article: ' . siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Article Tags -->
        <?php if (!empty($article['tags'])): ?>
        <section class="article-tags">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h4>Tags</h4>
                        <div class="tags-list">
                            <?php 
                            $tags = explode(',', $article['tags']);
                            foreach ($tags as $tag): 
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                            <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Related Articles -->
        <?php if (!empty($related_articles)): ?>
        <section class="related-articles">
            <div class="container">
                <div class="section-header text-center">
                    <h2>Related Articles</h2>
                    <p>More insights and updates from our team</p>
                </div>
                
                <div class="related-grid">
                    <?php foreach ($related_articles as $related): ?>
                    <article class="related-card">
                        <img src="<?php echo $related['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=200&fit=crop'; ?>" 
                             alt="<?php echo htmlspecialchars($related['title']); ?>" 
                             class="loading">
                        <div class="related-card-content">
                            <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <h3><?php echo htmlspecialchars($related['title']); ?></h3>
                            <p><?php echo htmlspecialchars(substr($related['excerpt'], 0, 120)) . '...'; ?></p>
                            <div class="date"><?php echo date('F j, Y', strtotime($related['published_at'])); ?></div>
                            <a href="<?php echo siteUrl('news/' . $related['slug']); ?>" class="btn btn-outline" style="margin-top: 1rem;">Read More</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Newsletter CTA -->
        <section class="newsletter-cta" style="background: var(--primary-color); color: white; padding: 4rem 0;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2>Stay Updated</h2>
                        <p class="lead">Get the latest insights on architecture and design trends delivered to your inbox.</p>
                        <form class="newsletter-form" style="display: flex; gap: 1rem; max-width: 400px; margin: 2rem auto 0;">
                            <input type="email" placeholder="Your email address" style="flex: 1; padding: 0.75rem; border: none; border-radius: 4px;">
                            <button type="submit" class="btn" style="background: var(--accent-color); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px;">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Links -->
        <section style="padding: 2rem 0; background: #F8F9FA;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <a href="<?php echo siteUrl('news'); ?>" class="btn btn-outline">← Back to News</a>
                            <a href="<?php echo siteUrl('contact'); ?>" class="btn btn-primary">Get In Touch</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- Scripts -->
    <script src="<?php echo themeUrl('js/main.js'); ?>"></script>
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    
    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</body>
</html>
