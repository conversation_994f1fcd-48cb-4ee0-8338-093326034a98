<?php
/**
 * News Details Page - Individual Article/Blog Post
 * Complete single-page template for news content display
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get news slug from URL
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ' . siteUrl('news'));
    exit;
}

// Fetch news article from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND status = 'published'");
$stmt->execute([$slug]);
$article = $stmt->fetch();

if (!$article) {
    header('Location: ' . siteUrl('404'));
    exit;
}

// Get related articles
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE category = ? AND id != ? AND status = 'published' ORDER BY published_at DESC LIMIT 3");
$stmt->execute([$article['category'], $article['id']]);
$related_articles = $stmt->fetchAll();

// Get recent articles if no related articles found
if (empty($related_articles)) {
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE id != ? AND status = 'published' ORDER BY published_at DESC LIMIT 3");
    $stmt->execute([$article['id']]);
    $related_articles = $stmt->fetchAll();
}

// Get next and previous articles for navigation
$stmt = $db->prepare("SELECT id, slug, title FROM blog_posts WHERE id > ? AND status = 'published' ORDER BY id ASC LIMIT 1");
$stmt->execute([$article['id']]);
$next_article = $stmt->fetch();

$stmt = $db->prepare("SELECT id, slug, title FROM blog_posts WHERE id < ? AND status = 'published' ORDER BY id DESC LIMIT 1");
$stmt->execute([$article['id']]);
$prev_article = $stmt->fetch();

$pageTitle = $article['title'] . ' - ' . SITE_NAME;
$pageDescription = $article['excerpt'] ?: 'Read our latest insights on ' . $article['title'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:image" content="<?php echo $article['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    <meta property="og:url" content="<?php echo siteUrl('news/' . $article['slug']); ?>">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo $article['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    
    <!-- Article-specific styles -->
    <style>
        /* Breadcrumb Navigation */
        .breadcrumb-nav {
            background: #F8F9FA;
            padding: 1rem 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-nav .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
        }
        
        .breadcrumb a {
            color: var(--accent-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .breadcrumb a:hover {
            color: #d66e1a;
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: #adb5bd;
        }
        
        /* Article Hero Section */
        .article-hero {
            height: 60vh;
            background-size: cover;
            background-position: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            min-height: 500px;
        }
        
        .article-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(rgba(26, 26, 26, 0.5), rgba(26, 26, 26, 0.7));
            z-index: 1;
        }
        
        .article-hero-content {
            position: relative;
            z-index: 2;
            max-width: 900px;
            padding: 2rem;
        }
        
        .article-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
            line-height: 1.2;
        }
        
        .article-category {
            display: inline-block;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }
        
        /* Article Meta Information */
        .article-meta {
            background: #F8F9FA;
            padding: 2rem 0;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .meta-info {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .meta-item strong {
            color: var(--text-color);
            font-weight: 600;
        }
        
        .meta-icon {
            width: 18px;
            height: 18px;
            fill: var(--accent-color);
        }
        
        /* Article Content */
        .article-content {
            padding: 4rem 0;
            background: white;
        }
        
        .article-body {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            line-height: 1.8;
        }
        
        .article-body h2,
        .article-body h3,
        .article-body h4 {
            color: var(--text-color);
            margin: 2rem 0 1rem;
            font-weight: 600;
        }
        
        .article-body h2 {
            font-size: 1.8rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }
        
        .article-body h3 {
            font-size: 1.4rem;
            color: var(--accent-color);
        }
        
        .article-body p {
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            color: #333;
        }
        
        .article-body ul,
        .article-body ol {
            margin: 1.5rem 0;
            padding-left: 2rem;
        }
        
        .article-body li {
            margin-bottom: 0.5rem;
        }
        
        .article-body blockquote {
            background: #F8F9FA;
            border-left: 4px solid var(--accent-color);
            padding: 1.5rem;
            margin: 2rem 0;
            font-style: italic;
            font-size: 1.1rem;
        }
        
        .article-body img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 2rem 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        /* Social Share Buttons */
        .share-section {
            background: #F8F9FA;
            padding: 3rem 0;
            text-align: center;
        }
        
        .share-buttons {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .share-buttons h4 {
            color: var(--text-color);
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .share-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-email { background: #666; }
        
        /* Article Tags */
        .article-tags {
            padding: 3rem 0;
            background: white;
            border-top: 1px solid #E9ECEF;
        }
        
        .tags-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }
        
        .tags-container h4 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }
        
        .tags-list {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .tag {
            display: inline-block;
            background: #E9ECEF;
            color: var(--text-color);
            padding: 0.4rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .tag:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }
        
        /* Related Articles */
        .related-articles {
            padding: 4rem 0;
            background: #F8F9FA;
        }
        
        .related-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section-title {
            text-align: center;
            color: var(--text-color);
            font-size: 2rem;
            margin-bottom: 3rem;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            color: var(--text-color);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        
        .related-card h3 a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .related-card h3 a:hover {
            color: var(--accent-color);
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }
        
        .related-card .date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* Navigation Buttons */
        .article-navigation {
            padding: 3rem 0;
            background: white;
            border-top: 1px solid #E9ECEF;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: color 0.3s ease;
            max-width: 300px;
        }
        
        .nav-link:hover {
            color: var(--accent-color);
        }
        
        .nav-link.prev {
            text-align: left;
        }
        
        .nav-link.next {
            text-align: right;
            flex-direction: row-reverse;
        }
        
        .nav-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.3rem;
        }
        
        .nav-title {
            font-weight: 600;
            line-height: 1.3;
        }
        
        .back-to-news {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--accent-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .back-to-news:hover {
            background: #d66e1a;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
            color: white;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .article-hero h1 {
                font-size: 2rem;
            }
            
            .article-hero-content {
                padding: 1rem;
            }
            
            .meta-info {
                flex-direction: column;
                gap: 1rem;
            }
            
            .share-links {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-container {
                flex-direction: column;
                text-align: center;
            }
            
            .related-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 480px) {
            .article-body {
                padding: 0 1rem;
            }
            
            .article-hero h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Breadcrumb Navigation -->
    <section class="breadcrumb-nav">
        <div class="container">
            <nav class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo siteUrl('news'); ?>">News</a>
                <span class="breadcrumb-separator">›</span>
                <span><?php echo htmlspecialchars($article['title']); ?></span>
            </nav>
        </div>
    </section>

    <!-- Article Hero Section -->
    <section class="article-hero" style="background-image: url('<?php echo $article['featured_image'] ?: themeUrl('images/hero-bg-1.jpg'); ?>');">
        <div class="article-hero-content">
            <?php if ($article['category']): ?>
                <div class="article-category"><?php echo htmlspecialchars($article['category']); ?></div>
            <?php endif; ?>
            <h1><?php echo htmlspecialchars($article['title']); ?></h1>
            <?php if ($article['excerpt']): ?>
                <p style="font-size: 1.2rem; margin-top: 1rem; opacity: 0.9;"><?php echo htmlspecialchars($article['excerpt']); ?></p>
            <?php endif; ?>
        </div>
    </section>

    <!-- Article Meta Information -->
    <section class="article-meta">
        <div class="container">
            <div class="meta-info">
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H5V21H19V9Z"/>
                    </svg>
                    <span><strong>Published:</strong> <?php echo date('F j, Y', strtotime($article['published_at'])); ?></span>
                </div>
                <?php if ($article['author']): ?>
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                    </svg>
                    <span><strong>Author:</strong> <?php echo htmlspecialchars($article['author']); ?></span>
                </div>
                <?php endif; ?>
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
                    </svg>
                    <span><strong>Read Time:</strong> <?php echo max(1, ceil(str_word_count(strip_tags($article['content'])) / 200)); ?> min read</span>
                </div>
                <?php if ($article['category']): ?>
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M17.63,5.84C17.27,5.33 16.67,5 16,5L5,5.01C3.9,5.01 3,5.9 3,7V17C3,18.1 3.9,19 5,19H16C16.67,19 17.27,18.67 17.63,18.16L22,12L17.63,5.84Z"/>
                    </svg>
                    <span><strong>Category:</strong> <?php echo htmlspecialchars($article['category']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Article Content -->
    <section class="article-content">
        <div class="container">
            <article class="article-body">
                <?php echo $article['content']; ?>
            </article>
        </div>
    </section>

    <!-- Article Tags -->
    <?php if (!empty($article['tags'])): ?>
    <section class="article-tags">
        <div class="container">
            <div class="tags-container">
                <h4>Article Tags</h4>
                <div class="tags-list">
                    <?php 
                    $tags = explode(',', $article['tags']);
                    foreach ($tags as $tag): 
                        $tag = trim($tag);
                        if (!empty($tag)):
                    ?>
                        <a href="<?php echo siteUrl('news?tag=' . urlencode($tag)); ?>" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Social Share Section -->
    <section class="share-section">
        <div class="container">
            <div class="share-buttons">
                <h4>Share This Article</h4>
                <div class="share-links">
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                       target="_blank" class="share-link share-facebook">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>&text=<?php echo urlencode($article['title']); ?>" 
                       target="_blank" class="share-link share-twitter">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                       target="_blank" class="share-link share-linkedin">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                    </a>
                    <a href="mailto:?subject=<?php echo urlencode($article['title']); ?>&body=<?php echo urlencode('Check out this article: ' . siteUrl('news/' . $article['slug'])); ?>" 
                       class="share-link share-email">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 .02l-12 6.51v11.47h24v-11.47zm0 2.03l8.74 4.67h-17.48zm-10 6.47h20v9h-20z"/>
                        </svg>
                        Email
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Article Navigation -->
    <section class="article-navigation">
        <div class="container">
            <div class="nav-container">
                <?php if ($prev_article): ?>
                <a href="<?php echo siteUrl('news/' . $prev_article['slug']); ?>" class="nav-link prev">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                    <div>
                        <div class="nav-label">Previous Article</div>
                        <div class="nav-title"><?php echo htmlspecialchars($prev_article['title']); ?></div>
                    </div>
                </a>
                <?php endif; ?>
                
                <a href="<?php echo siteUrl('news'); ?>" class="back-to-news">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    </svg>
                    Back to News
                </a>
                
                <?php if ($next_article): ?>
                <a href="<?php echo siteUrl('news/' . $next_article['slug']); ?>" class="nav-link next">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                    <div>
                        <div class="nav-label">Next Article</div>
                        <div class="nav-title"><?php echo htmlspecialchars($next_article['title']); ?></div>
                    </div>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Related Articles -->
    <?php if (!empty($related_articles)): ?>
    <section class="related-articles">
        <div class="container">
            <div class="related-container">
                <h2 class="section-title">Related Articles</h2>
                <div class="related-grid">
                    <?php foreach ($related_articles as $related): ?>
                    <div class="related-card">
                        <?php if ($related['featured_image']): ?>
                            <img src="<?php echo $related['featured_image']; ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <?php else: ?>
                            <img src="<?php echo themeUrl('images/hero-bg-1.jpg'); ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <?php endif; ?>
                        <div class="related-card-content">
                            <?php if ($related['category']): ?>
                                <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <?php endif; ?>
                            <h3><a href="<?php echo siteUrl('news/' . $related['slug']); ?>"><?php echo htmlspecialchars($related['title']); ?></a></h3>
                            <div class="date"><?php echo date('F j, Y', strtotime($related['published_at'])); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- JavaScript for smooth scrolling and interactions -->
    <script>
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add copy link functionality
        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(function() {
                // Show success message (you can customize this)
                const btn = event.target.closest('.share-link');
                const originalText = btn.innerHTML;
                btn.innerHTML = '✓ Copied!';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                }, 2000);
            });
        }

        // Open share links in popup windows
        document.querySelectorAll('.share-link[target="_blank"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.href;
                const width = 600;
                const height = 400;
                const left = (screen.width - width) / 2;
                const top = (screen.height - height) / 2;
                
                window.open(url, 'share', `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`);
            });
        });
    </script>
</body>
</html>
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .meta-item strong {
            color: var(--accent-color);
        }
        
        .article-content {
            padding: 4rem 0;
        }
        
        .article-body {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
        }
        
        .article-body h2,
        .article-body h3,
        .article-body h4 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .article-body h2 {
            font-size: 1.8rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }
        
        .article-body h3 {
            font-size: 1.4rem;
            color: var(--accent-color);
        }
        
        .article-body p {
            margin-bottom: 1.5rem;
        }
        
        .article-body ul,
        .article-body ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }
        
        .article-body li {
            margin-bottom: 0.5rem;
        }
        
        .article-body blockquote {
            background: #F8F9FA;
            border-left: 4px solid var(--accent-color);
            padding: 1.5rem;
            margin: 2rem 0;
            font-style: italic;
        }
        
        .article-tags {
            background: #F8F9FA;
            padding: 2rem 0;
            border-top: 1px solid #E9ECEF;
        }
        
        .tags-list {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .tag {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .tag:hover {
            background: #d35400;
            color: white;
        }
        
        .related-articles {
            padding: 4rem 0;
            background: #F5F5F5;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-size: 1.2rem;
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .related-card .date {
            color: #666;
            font-size: 0.85rem;
            margin-top: 1rem;
        }
        
        .share-buttons {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid #E9ECEF;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .share-buttons h4 {
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        
        .share-link:hover {
            transform: scale(1.1);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-email { background: #666; }
        
        @media (max-width: 768px) {
            .article-hero h1 {
                font-size: 1.8rem;
            }
            
            .meta-info {
                gap: 1rem;
                justify-content: flex-start;
            }
            
            .article-body {
                font-size: 1rem;
            }
            
            .share-links {
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <!-- Article Hero -->
        <section class="article-hero" style="background-image: url('<?php echo $article['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop'; ?>');">
            <div class="article-hero-content">
                <h1><?php echo htmlspecialchars($article['title']); ?></h1>
                <p class="lead"><?php echo htmlspecialchars($article['excerpt']); ?></p>
            </div>
        </section>

        <!-- Article Meta -->
        <section class="article-meta">
            <div class="container">
                <div class="meta-info">
                    <div class="meta-item">
                        <strong>Author:</strong> <?php echo htmlspecialchars($article['author'] ?: 'Monolith Design Team'); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Published:</strong> <?php echo date('F j, Y', strtotime($article['published_at'])); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Category:</strong> <?php echo htmlspecialchars($article['category']); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Reading Time:</strong> <?php echo ceil(str_word_count(strip_tags($article['content'])) / 200); ?> min
                    </div>
                </div>
            </div>
        </section>

        <!-- Article Content -->
        <section class="article-content">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="article-body">
                            <?php echo $article['content']; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Share Buttons -->
        <section class="share-buttons">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <h4>Share This Article</h4>
                        <div class="share-links">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-facebook" target="_blank" rel="noopener">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>&text=<?php echo urlencode($article['title']); ?>" 
                               class="share-link share-twitter" target="_blank" rel="noopener">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-linkedin" target="_blank" rel="noopener">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="mailto:?subject=<?php echo urlencode($article['title']); ?>&body=<?php echo urlencode('Check out this article: ' . siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Article Tags -->
        <?php if (!empty($article['tags'])): ?>
        <section class="article-tags">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h4>Tags</h4>
                        <div class="tags-list">
                            <?php 
                            $tags = explode(',', $article['tags']);
                            foreach ($tags as $tag): 
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                            <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Related Articles -->
        <?php if (!empty($related_articles)): ?>
        <section class="related-articles">
            <div class="container">
                <div class="section-header text-center">
                    <h2>Related Articles</h2>
                    <p>More insights and updates from our team</p>
                </div>
                
                <div class="related-grid">
                    <?php foreach ($related_articles as $related): ?>
                    <article class="related-card">
                        <img src="<?php echo $related['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=200&fit=crop'; ?>" 
                             alt="<?php echo htmlspecialchars($related['title']); ?>" 
                             class="loading">
                        <div class="related-card-content">
                            <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <h3><?php echo htmlspecialchars($related['title']); ?></h3>
                            <p><?php echo htmlspecialchars(substr($related['excerpt'], 0, 120)) . '...'; ?></p>
                            <div class="date"><?php echo date('F j, Y', strtotime($related['published_at'])); ?></div>
                            <a href="<?php echo siteUrl('news/' . $related['slug']); ?>" class="btn btn-outline" style="margin-top: 1rem;">Read More</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Newsletter CTA -->
        <section class="newsletter-cta" style="background: var(--primary-color); color: white; padding: 4rem 0;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2>Stay Updated</h2>
                        <p class="lead">Get the latest insights on architecture and design trends delivered to your inbox.</p>
                        <form class="newsletter-form" style="display: flex; gap: 1rem; max-width: 400px; margin: 2rem auto 0;">
                            <input type="email" placeholder="Your email address" style="flex: 1; padding: 0.75rem; border: none; border-radius: 4px;">
                            <button type="submit" class="btn" style="background: var(--accent-color); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px;">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Links -->
        <section style="padding: 2rem 0; background: #F8F9FA;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <a href="<?php echo siteUrl('news'); ?>" class="btn btn-outline">← Back to News</a>
                            <a href="<?php echo siteUrl('contact'); ?>" class="btn btn-primary">Get In Touch</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- Scripts -->
    <script src="<?php echo themeUrl('js/main.js'); ?>"></script>
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    
    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</body>
</html>
