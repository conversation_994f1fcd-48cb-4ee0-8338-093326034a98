<?php
/**
 * Monolith Design Co. - Configuration File
 * Contains all site-wide settings and database configuration
 */

// Prevent direct access
if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Site Configuration
define('SITE_NAME', 'Monolith Design Co.');
define('SITE_TAGLINE', 'Engineering the Future of Structures');
define('SITE_URL', 'http://localhost/monolith-design');
define('THEME_PATH', SITE_URL . '/assets');
define('ADMIN_PATH', SITE_URL . '/admin');

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'monolith_design');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_CHARSET', 'utf8mb4');

// Design System Constants
define('PRIMARY_COLOR', '#1A1A1A');
define('SECONDARY_COLOR', '#F5F5F5');
define('ACCENT_COLOR', '#E67E22');
define('TEXT_COLOR', '#333333');

// Admin Configuration
define('ADMIN_EMAIL', '<EMAIL>');
define('ADMIN_PASSWORD_HASH', password_hash('admin123', PASSWORD_DEFAULT));

// File Upload Settings
define('UPLOAD_PATH', __DIR__ . '/assets/images/uploads/');
define('UPLOAD_URL', SITE_URL . '/assets/images/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Error Reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS

// Timezone
date_default_timezone_set('UTC');

// Security
define('CSRF_TOKEN_NAME', 'monolith_csrf_token');
define('SESSION_LIFETIME', 7200); // 2 hours

// Theme Options (defaults - these will be overridden by database values)
$theme_options = [
    'site_logo' => THEME_PATH . '/images/logo.svg',
    'favicon' => THEME_PATH . '/images/favicon.ico',
    'accent_color' => ACCENT_COLOR,
    'phone_number' => '+****************',
    'email' => '<EMAIL>',
    'address' => '1247 Steel Avenue, Downtown District, Metropolis, CA 90210',
    'facebook_url' => 'https://facebook.com/monolithdesign',
    'twitter_url' => 'https://twitter.com/monolithdesign',
    'linkedin_url' => 'https://linkedin.com/company/monolithdesign',
    'instagram_url' => 'https://instagram.com/monolithdesign'
];

// Auto-create upload directory if it doesn't exist
if (!file_exists(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}
?>
