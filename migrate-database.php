<?php
/**
 * Database Migration Script
 * Updates the blog_posts table structure and adds sample data
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h2>Database Migration Script</h2>";

try {
    $db = Database::getConnection();
    
    // Check if blog_posts table exists and has the new columns
    $stmt = $db->query("DESCRIBE blog_posts");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $missing_columns = [];
    $required_columns = ['category', 'tags', 'status', 'meta_description', 'published_at'];
    
    foreach ($required_columns as $column) {
        if (!in_array($column, $columns)) {
            $missing_columns[] = $column;
        }
    }
    
    // Add missing columns
    if (!empty($missing_columns)) {
        echo "<p>Adding missing columns to blog_posts table...</p>";
        
        foreach ($missing_columns as $column) {
            switch ($column) {
                case 'category':
                    $db->exec("ALTER TABLE blog_posts ADD COLUMN category VARCHAR(100) DEFAULT 'General'");
                    echo "✅ Added category column<br>";
                    break;
                case 'tags':
                    $db->exec("ALTER TABLE blog_posts ADD COLUMN tags TEXT");
                    echo "✅ Added tags column<br>";
                    break;
                case 'status':
                    $db->exec("ALTER TABLE blog_posts ADD COLUMN status ENUM('draft', 'published') DEFAULT 'draft'");
                    echo "✅ Added status column<br>";
                    break;
                case 'meta_description':
                    $db->exec("ALTER TABLE blog_posts ADD COLUMN meta_description TEXT");
                    echo "✅ Added meta_description column<br>";
                    break;
                case 'published_at':
                    $db->exec("ALTER TABLE blog_posts ADD COLUMN published_at TIMESTAMP NULL");
                    echo "✅ Added published_at column<br>";
                    break;
            }
        }
    } else {
        echo "<p>✅ All required columns already exist.</p>";
    }
    
    // Check if we have any blog posts
    $stmt = $db->query("SELECT COUNT(*) FROM blog_posts");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "<p>Adding sample blog posts...</p>";
        
        // Insert sample blog posts
        $sample_posts = [
            [
                'title' => 'Sustainable Architecture: Building for Tomorrow',
                'slug' => 'sustainable-architecture-building-tomorrow',
                'excerpt' => 'Exploring innovative approaches to eco-friendly design and construction that minimize environmental impact while maximizing efficiency.',
                'content' => '<p>Sustainable architecture has evolved from a niche concept to a fundamental requirement in modern construction. As we face unprecedented environmental challenges, architects and designers are pioneering innovative solutions that harmonize human needs with ecological responsibility.</p><h3>Key Principles of Sustainable Design</h3><p>Our approach to sustainable architecture encompasses several core principles: energy efficiency, resource conservation, and environmental harmony. By integrating renewable energy systems, utilizing recycled materials, and designing for natural ventilation and lighting, we create buildings that significantly reduce their carbon footprint.</p>',
                'featured_image' => 'https://images.unsplash.com/photo-1518005020951-eccb494ad742?w=1200&h=600&fit=crop',
                'author' => 'Sarah Chen',
                'category' => 'Sustainability',
                'tags' => 'sustainable design, green building, eco-friendly, architecture',
                'status' => 'published',
                'meta_description' => 'Discover how sustainable architecture is shaping the future of construction with innovative eco-friendly design principles and technologies.',
                'published_at' => '2023-12-15 10:00:00'
            ],
            [
                'title' => 'Smart Building Technologies: The Integration Revolution',
                'slug' => 'smart-building-technologies-integration',
                'excerpt' => 'How IoT, AI, and automation are transforming modern buildings into intelligent, responsive environments that adapt to user needs.',
                'content' => '<p>The integration of smart technologies in modern buildings represents a paradigm shift in how we design, construct, and operate architectural spaces. From IoT sensors to AI-driven systems, buildings are becoming increasingly intelligent and responsive.</p><h3>The Internet of Things in Architecture</h3><p>IoT devices embedded throughout a building create a network of interconnected systems that monitor everything from air quality to occupancy patterns.</p>',
                'featured_image' => 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop',
                'author' => 'David Kim',
                'category' => 'Technology',
                'tags' => 'smart buildings, IoT, automation, AI, technology',
                'status' => 'published',
                'meta_description' => 'Explore how smart building technologies are revolutionizing modern architecture through IoT, AI, and intelligent automation systems.',
                'published_at' => '2023-12-10 14:30:00'
            ],
            [
                'title' => 'Reimagining Urban Spaces: Community-Centered Design',
                'slug' => 'urban-planning-community-design',
                'excerpt' => 'How modern urban planning prioritizes community needs, creating spaces that foster connection, sustainability, and quality of life.',
                'content' => '<p>Urban planning has undergone a fundamental transformation, shifting from car-centric development to people-focused design that prioritizes community well-being, environmental sustainability, and social connection.</p><h3>The Human-Scale Approach</h3><p>Modern urban design emphasizes human-scale development that encourages walking, cycling, and public transit use.</p>',
                'featured_image' => 'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=1200&h=600&fit=crop',
                'author' => 'Marcus Rodriguez',
                'category' => 'Urban Planning',
                'tags' => 'urban planning, community design, sustainability, city development',
                'status' => 'published',
                'meta_description' => 'Learn how community-centered urban planning is creating more sustainable, livable, and connected cities for the future.',
                'published_at' => '2023-12-08 16:45:00'
            ]
        ];
        
        $stmt = $db->prepare("INSERT INTO blog_posts (title, slug, excerpt, content, featured_image, author, category, tags, status, meta_description, published_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sample_posts as $post) {
            $stmt->execute([
                $post['title'],
                $post['slug'],
                $post['excerpt'],
                $post['content'],
                $post['featured_image'],
                $post['author'],
                $post['category'],
                $post['tags'],
                $post['status'],
                $post['meta_description'],
                $post['published_at']
            ]);
        }
        
        echo "✅ Added " . count($sample_posts) . " sample blog posts<br>";
    } else {
        echo "<p>✅ Blog posts already exist ($count posts found).</p>";
    }
    
    echo "<h3>✅ Migration completed successfully!</h3>";
    echo "<p><a href='news.php'>View News Page</a> | <a href='admin/blog.php'>Manage Blog Posts</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
