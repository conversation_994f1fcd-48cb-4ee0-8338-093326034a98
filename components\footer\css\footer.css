/**
 * Dedicated Footer CSS - Clean & Modular
 * NO BORDER RADIUS ANYWHERE - Sharp, clean design
 */

/* ===== FOOTER COMPONENT BASE ===== */
.footer-component {
    position: relative;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    padding: 80px 0 40px;
    overflow: hidden;
}

.footer-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.footer-content {
    position: relative;
    z-index: 1;
}

.footer-main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 60px;
    margin-bottom: 60px;
}

/* ===== BRAND SECTION ===== */
.footer-brand-section {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.footer-logo-wrapper {
    margin-bottom: 20px;
}

.footer-logo {
    height: 45px;
    width: auto;
    margin-bottom: 20px;
    display: block;
}

.footer-tagline {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
}

/* ===== NEWSLETTER SECTION - NO BORDER RADIUS ===== */
.newsletter-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 0 !important; /* NO BORDER RADIUS */
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.newsletter-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.newsletter-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 25px 0;
}

.newsletter-input-group {
    display: flex;
    gap: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0 !important; /* NO BORDER RADIUS */
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-input {
    flex: 1;
    padding: 14px 16px;
    border: none;
    background: transparent;
    color: #ffffff;
    font-size: 14px;
    border-radius: 0 !important; /* NO BORDER RADIUS */
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.newsletter-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 20px;
    background: var(--accent-color, #E67E22);
    color: #ffffff;
    border: none;
    border-radius: 0 !important; /* NO BORDER RADIUS */
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.newsletter-button:hover {
    background: #d35400;
    transform: translateX(2px);
}

/* ===== NAVIGATION SECTION ===== */
.footer-nav-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.footer-nav-column {
    display: flex;
    flex-direction: column;
}

.footer-nav-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    position: relative;
    padding-bottom: 10px;
}

.footer-nav-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color, #E67E22);
    border-radius: 0 !important; /* NO BORDER RADIUS */
}

.footer-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-nav-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 0;
}

.footer-nav-link:hover {
    color: #ffffff;
    padding-left: 8px;
}

/* ===== CONTACT SECTION - WITH CURVED BORDER ===== */
.footer-contact-section {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px; /* CURVED BORDER FOR CONTAINER */
    padding: 30px;
    backdrop-filter: blur(10px);
}

.footer-contact-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 25px 0;
    position: relative;
    padding-bottom: 10px;
}

.footer-contact-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color, #E67E22);
    border-radius: 0 !important; /* NO BORDER RADIUS */
}

.footer-contact-info-grid {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.footer-contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.footer-contact-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0; /* SQUARE ICONS */
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-contact-details {
    flex: 1;
    min-width: 0;
}

.footer-contact-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
}

.footer-contact-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

.footer-contact-links {
    margin: 0;
}

.footer-contact-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-contact-link:hover {
    color: var(--accent-color, #E67E22);
}

/* ===== FOOTER BOTTOM ===== */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 30px 0;
    margin-top: 60px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
}

.footer-copyright {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.copyright-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    margin: 0;
}

.footer-legal-links {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.footer-legal-link {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-legal-link:hover {
    color: var(--accent-color, #E67E22);
}

.footer-social {
    display: flex;
    align-items: center;
    gap: 20px;
}

.social-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
}

.social-links {
    display: flex;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0 !important; /* NO BORDER RADIUS */
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: var(--accent-color, #E67E22);
    color: #ffffff;
    transform: translateY(-2px);
    border-color: var(--accent-color, #E67E22);
}

/* ===== SELECTIVE BORDER RADIUS CONTROL ===== */
/* Keep border radius on main containers, remove from icons and inner elements */
.footer-contact-icon,
.footer-contact-icon *,
.social-link,
.social-link *,
.newsletter-input,
.newsletter-button {
    border-radius: 0 !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .footer-main-grid {
        grid-template-columns: 1fr 1.5fr 1fr;
        gap: 40px;
    }

    .footer-nav-section {
        gap: 30px;
    }
}

@media (max-width: 992px) {
    .footer-main-grid {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: left;
    }

    .footer-brand-section {
        order: 1;
    }

    .footer-nav-section {
        order: 2;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }

    .footer-contact-section {
        order: 3;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .footer-copyright {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .footer-component {
        padding: 60px 0 0;
    }

    .footer-main-grid {
        gap: 40px;
    }

    .footer-nav-section {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .newsletter-section {
        padding: 25px;
    }

    .newsletter-input-group {
        flex-direction: column;
    }

    .newsletter-input {
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .newsletter-button {
        justify-content: center;
    }

    .footer-contact-info-grid {
        gap: 20px;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .footer-component {
        padding: 40px 0 0;
    }

    .footer-main-grid {
        gap: 30px;
    }

    .footer-brand-section {
        gap: 25px;
    }

    .newsletter-section {
        padding: 20px;
    }

    .footer-bottom {
        padding: 25px 0;
        margin-top: 40px;
    }

    .footer-legal-links {
        flex-direction: column;
        gap: 10px;
    }
}
